[package]
edition = "2024"
name = "codex-protocol"
version = { workspace = true }

[lib]
name = "codex_protocol"
path = "src/lib.rs"

[lints]
workspace = true

[dependencies]
base64 = "0.22.1"
mcp-types = { path = "../mcp-types" }
mime_guess = "2.0.5"
serde = { version = "1", features = ["derive"] }
serde_bytes = "0.11"
serde_json = "1"
strum = "0.27.2"
strum_macros = "0.27.2"
tracing = "0.1.41"
ts-rs = { version = "11", features = ["uuid-impl", "serde-json-impl"] }
uuid = { version = "1", features = ["serde", "v4"] }

[dev-dependencies]
pretty_assertions = "1.4.1"
