[package]
edition = "2024"
name = "codex-ollama"
version = { workspace = true }

[lib]
name = "codex_ollama"
path = "src/lib.rs"

[lints]
workspace = true

[dependencies]
async-stream = "0.3"
bytes = "1.10.1"
codex-core = { path = "../core" }
futures = "0.3"
reqwest = { version = "0.12", features = ["json", "stream"] }
serde_json = "1"
tokio = { version = "1", features = [
    "io-std",
    "macros",
    "process",
    "rt-multi-thread",
    "signal",
] }
toml = "0.9.5"
tracing = { version = "0.1.41", features = ["log"] }
wiremock = "0.6"

[dev-dependencies]
tempfile = "3"
