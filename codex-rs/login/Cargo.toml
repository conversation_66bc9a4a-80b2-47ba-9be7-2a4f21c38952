[package]
edition = "2024"
name = "codex-login"
version = { workspace = true }

[lints]
workspace = true

[dependencies]
base64 = "0.22"
chrono = { version = "0.4", features = ["serde"] }
codex-core = { path = "../core" }
codex-protocol = { path = "../protocol" }
rand = "0.8"
reqwest = { version = "0.12", features = ["json", "blocking"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
sha2 = "0.10"
tempfile = "3"
thiserror = "2.0.16"
tiny_http = "0.12"
tokio = { version = "1", features = [
    "io-std",
    "macros",
    "process",
    "rt-multi-thread",
    "signal",
] }
url = "2"
urlencoding = "2.1"
webbrowser = "1.0"

[dev-dependencies]
pretty_assertions = "1.4.1"
tempfile = "3"
