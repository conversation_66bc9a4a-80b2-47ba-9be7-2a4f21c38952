name: 🧑‍💻 VS Code Extension
description: Report an issue with the VS Code extension
labels:
  - extension
  - needs triage
body:
  - type: markdown
    attributes:
      value: |
        Before submitting a new issue, please search for existing issues to see if your issue has already been reported.
        If it has, please add a 👍 reaction (no need to leave a comment) to the existing issue instead of creating a new one.

  - type: input
    id: version
    attributes:
      label: What version of the VS Code extension are you using?
  - type: input
    id: ide
    attributes:
      label: Which IDE are you using?
      description: Like `VS Code`, `Cursor`, `Windsurf`, etc.
  - type: input
    id: platform
    attributes:
      label: What platform is your computer?
      description: |
        For MacOS and Linux: copy the output of `uname -mprs`
        For Windows: copy the output of `"$([Environment]::OSVersion | ForEach-Object VersionString) $(if ([Environment]::Is64BitOperatingSystem) { "x64" } else { "x86" })"` in the PowerShell console
  - type: textarea
    id: steps
    attributes:
      label: What steps can reproduce the bug?
      description: Explain the bug and provide a code snippet that can reproduce it.
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: What is the expected behavior?
      description: If possible, please provide text instead of a screenshot.
  - type: textarea
    id: actual
    attributes:
      label: What do you see instead?
      description: If possible, please provide text instead of a screenshot.
  - type: textarea
    id: notes
    attributes:
      label: Additional information
      description: Is there anything else you think we should know?
