name: ci

on:
  pull_request: { branches: [main] }
  push: { branches: [main] }

jobs:
  build-test:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    env:
      NODE_OPTIONS: --max-old-space-size=4096
    steps:
      - name: Checkout repository
        uses: actions/checkout@v5

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10.8.1
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "store_path=$(pnpm store path --silent)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ steps.pnpm-cache.outputs.store_path }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      # Run all tasks using workspace filters

      - name: Ensure staging a release works.
        env:
          GH_TOKEN: ${{ github.token }}
        run: ./codex-cli/scripts/stage_release.sh

      - name: Ensure root README.md contains only ASCII and certain Unicode code points
        run: ./scripts/asciicheck.py README.md
      - name: Check root README ToC
        run: python3 scripts/readme_toc.py README.md

      - name: Ensure codex-cli/README.md contains only ASCII and certain Unicode code points
        run: ./scripts/asciicheck.py codex-cli/README.md
      - name: Check codex-cli/README ToC
        run: python3 scripts/readme_toc.py codex-cli/README.md
