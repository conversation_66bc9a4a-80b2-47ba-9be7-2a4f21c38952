{"outputs": {"codex": {"platforms": {"macos-aarch64": {"regex": "^codex-aarch64-apple-darwin\\.zst$", "path": "codex"}, "macos-x86_64": {"regex": "^codex-x86_64-apple-darwin\\.zst$", "path": "codex"}, "linux-x86_64": {"regex": "^codex-x86_64-unknown-linux-musl\\.zst$", "path": "codex"}, "linux-aarch64": {"regex": "^codex-aarch64-unknown-linux-musl\\.zst$", "path": "codex"}, "windows-x86_64": {"regex": "^codex-x86_64-pc-windows-msvc\\.exe\\.zst$", "path": "codex.exe"}, "windows-aarch64": {"regex": "^codex-aarch64-pc-windows-msvc\\.exe\\.zst$", "path": "codex.exe"}}}}}